import { Router } from 'express';
import { DomainValueController } from '../controllers/domain-value.controller';
import { validateBody } from '../middlewares/validators/validation.middleware';
import { domainValueSchema } from '../validators/domain-value.validator';

const router: Router = Router();

const domainValueController = DomainValueController.getInstance();

router.route('/').get(domainValueController.getAll).post(validateBody(domainValueSchema), domainValueController.create);

router
  .route('/:id')
  .get(domainValueController.getById)
  .put(validateBody(domainValueSchema), domainValueController.update)
  .delete(domainValueController.delete);

export default router;
