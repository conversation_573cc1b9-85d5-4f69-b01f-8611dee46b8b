export const NODE_ENV = process.env.NODE_ENV || 'development';

export const FIREBASE_CLIENT_EMAIL = process.env.FIREBASE_CLIENT_EMAIL as string;
export const FIREBASE_PRIVATE_KEY = process.env.FIREBASE_PRIVATE_KEY as string;
export const FIREBASE_PROJECT_ID = process.env.FIREBASE_PROJECT_ID as string;
export const PORT = process.env.PORT || 5000;

export const MAIL_SMTP_HOST = process.env.SMTP_HOST;
export const MAIL_SMTP_PORT = Number(process.env.SMTP_PORT);
export const MAIL_IS_SECURE = process.env.SMTP_SECURE === 'true';
export const MAIL_USERNAME = process.env.SMTP_USER as string;
export const MAIL_PASSWORD = process.env.SMTP_PASSWORD;
export const MAIL_FROM = process.env.SMTP_FROM as string;
export const ERROR_EMAIL_RECIPIENT = process.env.ERROR_EMAIL_RECIPIENT as string;

export const GUEST_FRONTEND_URL = process.env.GUEST_FRONTEND_URL || 'https://app.staytransit.com';
export const DASHBOARD_FRONTEND_URL = process.env.DASHBOARD_FRONTEND_URL || 'https://live.staytransit.com';

export const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY as string;

export const AWS_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID as string;
export const AWS_SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY as string;
export const AWS_S3_BUCKET_NAME = process.env.AWS_S3_BUCKET as string;
export const AWS_REGION = process.env.AWS_REGION as string;

export const JWT_ACCESS_TOKEN_SECRET = process.env.ACCESS_TOKEN_SECRET as string;
export const JWT_ACCESS_TOKEN_EXPIRATION_TIME = '15m';
export const JWT_ALGORITHM = 'HS256';
export const JWT_ISSUER = 'staytransit.com';
export const JWT_AUDIENCE = 'staytransit.com';

export const JWT_REFRESH_TOKEN_SECRET = process.env.REFRESH_TOKEN_SECRET as string;
export const JWT_REFRESH_TOKEN_EXPIRATION_TIME = '7d';

export const JWT_MISC_TOKEN_SECRET = process.env.MISC_TOKEN_SECRET as string;

export const ALLOWED_ORIGINS: string[] = (process.env.ALLOWED_ORIGINS || 'http://localhost:5173').split(',');
export const ALLOWED_HEADERS: string[] = (process.env.ALLOWED_HEADERS || 'Content-Type,Authorization').split(',');

export const MONGO_URI = process.env.MONGO_URI as string;
