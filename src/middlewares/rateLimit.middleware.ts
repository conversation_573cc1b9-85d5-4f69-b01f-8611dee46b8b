import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';

/**
 * Rate limiting middleware to prevent abuse and DDoS attacks
 * @param req
 * @param res
 * @param next
 * @returns void
 */

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  limit: 100,
  message: {
    error: 'Too many requests from this IP. Please try again later.',
    status: 429,
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const rateLimitMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  limiter(req, res, next);
};

export default rateLimitMiddleware;
