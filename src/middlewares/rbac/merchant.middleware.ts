import { ResponseUtil } from '../../utils/response';
import { INextFunction, IRequest, IResponse, UserRoleEnum } from '../../types';

export const merchantMiddleware = (req: IRequest, res: IResponse, next: INextFunction): void => {
  const role = req.user?.role;
  if (!role) {
    return ResponseUtil.unauthorized(res, 'Unauthorized');
  }
  if (role === UserRoleEnum.USER) {
    return next();
  }
  if (role === UserRoleEnum.SUPERADMIN) {
    return next();
  }
  const propertyId = req.params.propertyId;
  if (!propertyId) {
    return ResponseUtil.badRequest(res, 'Property ID is required');
  }

  const propertyIds = req.user?.properties;

  if (!propertyIds || !propertyIds.includes(propertyId)) {
    return ResponseUtil.forbidden(res, 'You do not have access to this property');
  }
  next();
};
