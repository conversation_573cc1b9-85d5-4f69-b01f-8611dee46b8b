import UserSchema from '../models/user.model';
import bcrypt from 'bcryptjs';
import { EmailService } from '../services/email.service';
import Property from '../models/property.model';
import UserTokenDocument from '../models/user-token.model';
import { admin } from '../config/firebase';
import { JwtService } from '../services/jwt.service';
import { NODE_ENV } from '../constants';
import { IProperty, IRequest, IResponse, IUser, PropertyStatusEnum, UserRoleEnum } from '../types';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';
import loggerService from '../utils/logger/logger.service';

interface TokenValidationResponse {
  email?: string;
  firstName: string;
  lastName: string;
}

export class AuthenticationController {
  private static instance: AuthenticationController;
  private emailService: EmailService;
  private jwtService: JwtService;

  private constructor() {
    this.emailService = new EmailService();
    this.jwtService = JwtService.getInstance();
  }
  public static getInstance(): AuthenticationController {
    if (!AuthenticationController.instance) {
      AuthenticationController.instance = new AuthenticationController();
    }
    return AuthenticationController.instance;
  }

  register = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { email, password } = req.body;
    const userName = this.generateId(10);

    const existingUser = await UserSchema.findOne({ email });
    if (existingUser) {
      return ResponseUtil.conflict(res, 'User already exists');
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const newUser = new UserSchema({ ...req.body, password: hashedPassword, userName, active: false });

    await newUser.save();

    const token = this.jwtService.generateMiscToken(newUser._id.toString(), 60 * 24);

    await UserTokenDocument.deleteMany({ userId: newUser._id });

    await UserTokenDocument.create({
      userId: newUser._id,
      token: token,
    });

    void this.emailService.sendRegistrationEmail(email, newUser, token);

    ResponseUtil.created(res, 'User registered successfully', null);
  });

  login = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { email, password } = req.body;

    const user = await UserSchema.findOne({ email }).select('+password');

    if (!user) {
      return ResponseUtil.badRequest(res, 'Invalid credentials');
    }

    if (!user.active) {
      return ResponseUtil.badRequest(res, 'User is not active, Please verify your email');
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return ResponseUtil.badRequest(res, 'Invalid credentials');
    }

    const properties = await Property.find({
      owner: user._id,
      active: true,
      status: PropertyStatusEnum.APPROVED,
    }).select('_id');

    this.setCookies(
      res,
      user._id.toString(),
      user.role,
      properties.map((p) => p._id.toString()),
    );

    ResponseUtil.success(res, 'Login successful');
  });

  profile = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { includeProperties } = req.query;
    const user = await UserSchema.findById(req.user?.userId).select('-password -__v -updatedAt');
    if (!user) {
      return ResponseUtil.notFound(res, 'User not found');
    }

    const userData: IUser & { properties?: IProperty[] } = user.toObject();
    if (req.user?.role === UserRoleEnum.MERCHANT || includeProperties === 'true') {
      userData.properties = await Property.find({
        owner: user._id,
        active: true,
        status: PropertyStatusEnum.APPROVED,
      })
        .select('-createdAt -updatedAt -__v -deleted -owner -status')
        .populate([
          {
            path: 'serviceType',
            select: 'name',
          },
        ]);
    }

    ResponseUtil.success(res, 'User profile retrieved successfully', userData);
  });

  forgotPassword = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { email } = req.body;
    const user = await UserSchema.findOne({ email });
    if (!user) {
      throw new Error('User not found');
    }

    const token = this.jwtService.generateMiscToken(user._id.toString(), 60);

    await UserTokenDocument.deleteMany({ userId: user._id });

    await UserTokenDocument.create({
      userId: user._id,
      token: token,
    });

    void this.emailService.sendForgotPasswordEmail(user.email, user.firstName, token);

    ResponseUtil.success(res, 'Email sent for reset password');
  });

  resetPassword = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const token = req.header('Authorization')?.split(' ')[1];

    if (!token) {
      return ResponseUtil.unauthorized(res, 'No token, authorization denied');
    }

    const tokenData = this.jwtService.verifyMiscToken(token);

    const userId = tokenData.userId;

    const { newPassword } = req.body;

    const resetDoc = await UserTokenDocument.findOne({ userId });

    if (!resetDoc) {
      throw new Error('Invalid or expired token');
    }

    if (!tokenData) {
      throw new Error('Invalid token');
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await UserSchema.findByIdAndUpdate(userId, {
      password: hashedPassword,
    });
    await UserTokenDocument.deleteMany({ userId });

    ResponseUtil.success(res, 'Password reset successfully');
  });

  changePassword = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    if (!req.user?.userId) {
      return ResponseUtil.unauthorized(res, 'Unauthorized');
    }
    const { currentPassword, newPassword } = req.body;

    const user = await UserSchema.findById(req.user.userId);

    if (!user || !(await bcrypt.compare(currentPassword, user.password))) {
      throw new Error('Invalid credentials');
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await UserSchema.findByIdAndUpdate(req.user.userId, {
      password: hashedPassword,
    });

    ResponseUtil.success(res, 'Password changed successfully');
  });

  checkUserExists = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { email } = req.body;
    const user = await UserSchema.findOne({ email });
    if (!user) {
      return ResponseUtil.success(res, 'User does not exist', false);
    }
    ResponseUtil.success(res, 'User already exists', true);
  });

  loginWithFirebase = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { firebaseToken } = req.body;
    const { phoneNumber, countryCode } = req.body.phone || {};

    if (!firebaseToken) {
      return ResponseUtil.badRequest(res, 'Firebase token is required');
    }

    const verifiedUser = await this.verifyFirebaseToken(firebaseToken);

    const isEmail = !!verifiedUser.email;

    let existingUser = await UserSchema.findOne(
      !isEmail
        ? {
            phone: {
              phoneNumber: phoneNumber,
              countryCode: countryCode,
            },
          }
        : {
            email: verifiedUser.email,
          },
    );

    const hashedPassword = await bcrypt.hash(this.generateRandomPassword(), 10);
    if (!existingUser) {
      const newUser = {
        userName: this.generateId(10),
        firstName: verifiedUser.firstName,
        lastName: verifiedUser.lastName,
        email: verifiedUser.email,
        password: hashedPassword,
        phone: {
          phoneNumber: phoneNumber,
          countryCode: countryCode,
        },
        role: UserRoleEnum.USER,
      };
      existingUser = await UserSchema.create(newUser);
    }

    const userId = existingUser._id.toString();
    const role = existingUser.role;
    this.setCookies(res, userId, role);
    ResponseUtil.success(res, 'Login successful');
  });

  logout = asyncRequestHandler(async (_req: IRequest, res: IResponse) => {
    res.clearCookie('accessToken', {
      httpOnly: true,
      secure: NODE_ENV !== 'development',
      sameSite: NODE_ENV === 'production' ? 'lax' : 'none',
      path: '/',
      partitioned: NODE_ENV !== 'production',
    });
    res.clearCookie('refreshToken', {
      httpOnly: true,
      secure: NODE_ENV !== 'development',
      sameSite: NODE_ENV === 'production' ? 'lax' : 'none',
      path: '/',
      partitioned: NODE_ENV !== 'production',
    });
    ResponseUtil.success(res, 'Logout successful');
  });

  verifyAccount = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const token = req.header('Authorization')?.split(' ')[1].trim();
    if (!token) {
      return ResponseUtil.badRequest(res, 'Token is required');
    }
    const tokenData = this.jwtService.verifyMiscToken(token);

    const userId = tokenData.userId;
    const resetDoc = await UserTokenDocument.findOne({ userId });

    if (!resetDoc) {
      throw new Error('Invalid or expired token');
    }

    const user = await UserSchema.findByIdAndUpdate(userId, { active: true }, { new: true }).select(
      'email firstName lastName',
    );
    await UserTokenDocument.deleteMany({ userId });

    ResponseUtil.success(res, 'Account verified successfully', user);
  });

  async verifyFirebaseToken(idToken: string): Promise<TokenValidationResponse> {
    try {
      const decodedToken = await admin.auth().verifyIdToken(idToken);

      const [firstName, ...lastName] = (decodedToken.name || 'Hi Traveller').split(' ');
      return {
        email: decodedToken.email,
        firstName,
        lastName: lastName.join(' '),
      };
    } catch (error) {
      loggerService.error(`Failed to verify Firebase token`, 'authentication.controller.ts', error);
      throw new Error('Invalid Firebase token');
    }
  }

  private generateRandomPassword(): string {
    return Math.random().toString(36).slice(-10);
  }

  private generateId(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    return Array.from({ length }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
  }

  private setCookies(res: IResponse, userId: string, role: string, properties?: string[]) {
    const accessToken = this.jwtService.generateAccessToken({
      userId,
      role,
      properties,
    });
    const refreshToken = this.jwtService.generateRefreshToken({
      userId,
      role,
      properties,
    });
    res.cookie('accessToken', accessToken, {
      httpOnly: true,
      secure: NODE_ENV !== 'development',
      sameSite: NODE_ENV === 'production' ? 'lax' : 'none',
      maxAge: 15 * 60 * 1000,
      path: '/',
      partitioned: NODE_ENV !== 'production',
    });
    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: NODE_ENV !== 'development',
      sameSite: NODE_ENV === 'production' ? 'lax' : 'none',
      maxAge: 7 * 24 * 60 * 60 * 1000,
      path: '/',
      partitioned: NODE_ENV !== 'production',
    });
  }
}
